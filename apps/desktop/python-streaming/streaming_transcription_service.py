#!/usr/bin/env python3
"""
Real-time Streaming Transcription Service

This service provides real-time speech-to-text transcription using faster-whisper.
It communicates with Node.js via stdin/stdout JSON protocol and processes audio
chunks to provide both interim and final transcription results.

Communication Protocol:
- Input (stdin): JSON messages with audio data and commands
- Output (stdout): JSON messages with transcription results and status

Message Types:
- Input: {"type": "audio_chunk", "data": "base64_audio", "sample_rate": 16000}
- Input: {"type": "finalize", "session_id": "uuid"}
- Output: {"type": "interim", "text": "partial text", "confidence": 0.8}
- Output: {"type": "final", "text": "final text", "confidence": 0.95}
- Output: {"type": "error", "message": "error description"}
"""

import sys
import json
import base64
import numpy as np
import soundfile as sf
from io import BytesIO
import threading
import queue
import time
from collections import deque
from faster_whisper import WhisperModel
import logging

# Configure logging to stderr to avoid interfering with stdout communication
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

class StreamingTranscriptionService:
    """Real-time streaming transcription service using faster-whisper."""
    
    def __init__(self, model_size="base.en", device="cpu", compute_type="int8"):
        """Initialize the streaming transcription service."""
        self.model_size = model_size
        self.device = device
        self.compute_type = compute_type
        self.model = None
        
        # Audio processing parameters
        self.sample_rate = 16000
        self.chunk_duration = 1.0  # Process 1-second chunks
        self.overlap_duration = 0.5  # 0.5-second overlap for smooth transitions
        self.min_audio_length = 0.5  # Minimum audio length for processing
        
        # Audio buffer for accumulating chunks
        self.audio_buffer = deque()
        self.buffer_lock = threading.Lock()
        
        # Processing queue and thread
        self.processing_queue = queue.Queue()
        self.processing_thread = None
        self.is_running = False
        
        # Session management
        self.current_session_id = None
        self.session_audio = []
        
        logger.info(f"Initializing StreamingTranscriptionService with model: {model_size}")
        
    def initialize_model(self):
        """Initialize the Whisper model."""
        try:
            logger.info("Loading Whisper model...")
            self.model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=self.compute_type
            )
            logger.info("Whisper model loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            return False
    
    def start(self):
        """Start the streaming service."""
        if not self.initialize_model():
            self.send_error("Failed to initialize Whisper model")
            return False
            
        self.is_running = True
        self.processing_thread = threading.Thread(target=self._processing_worker)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        logger.info("Streaming transcription service started")
        self.send_status("ready")
        return True
    
    def stop(self):
        """Stop the streaming service."""
        self.is_running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=2.0)
        logger.info("Streaming transcription service stopped")
    
    def process_audio_chunk(self, audio_data, sample_rate=16000):
        """Process incoming audio chunk."""
        try:
            # Convert base64 to numpy array
            audio_bytes = base64.b64decode(audio_data)
            
            # Convert bytes to numpy array (assuming 16-bit PCM)
            audio_array = np.frombuffer(audio_bytes, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Resample if necessary (basic resampling)
            if sample_rate != self.sample_rate:
                # Simple resampling - in production, use librosa.resample
                ratio = self.sample_rate / sample_rate
                new_length = int(len(audio_array) * ratio)
                audio_array = np.interp(
                    np.linspace(0, len(audio_array), new_length),
                    np.arange(len(audio_array)),
                    audio_array
                )
            
            # Add to buffer
            with self.buffer_lock:
                self.audio_buffer.append(audio_array)
                self.session_audio.append(audio_array)
            
            # Queue for processing if we have enough audio
            self._queue_processing_if_ready()
            
        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
            self.send_error(f"Audio processing error: {str(e)}")
    
    def _queue_processing_if_ready(self):
        """Queue audio for processing if we have enough accumulated."""
        with self.buffer_lock:
            if len(self.audio_buffer) == 0:
                return
                
            # Calculate total duration
            total_samples = sum(len(chunk) for chunk in self.audio_buffer)
            total_duration = total_samples / self.sample_rate
            
            # Process if we have enough audio
            if total_duration >= self.chunk_duration:
                # Combine audio chunks
                combined_audio = np.concatenate(list(self.audio_buffer))
                
                # Queue for processing
                self.processing_queue.put({
                    'type': 'interim',
                    'audio': combined_audio,
                    'timestamp': time.time()
                })
                
                # Keep overlap for next processing
                overlap_samples = int(self.overlap_duration * self.sample_rate)
                if len(combined_audio) > overlap_samples:
                    overlap_audio = combined_audio[-overlap_samples:]
                    self.audio_buffer.clear()
                    self.audio_buffer.append(overlap_audio)
                else:
                    self.audio_buffer.clear()
    
    def finalize_session(self, session_id):
        """Finalize the current session and process all remaining audio."""
        logger.info(f"Finalizing session: {session_id}")
        
        with self.buffer_lock:
            if self.session_audio:
                # Combine all session audio for final transcription
                final_audio = np.concatenate(self.session_audio)
                
                # Queue for final processing
                self.processing_queue.put({
                    'type': 'final',
                    'audio': final_audio,
                    'session_id': session_id,
                    'timestamp': time.time()
                })
                
                # Clear session data
                self.session_audio.clear()
                self.audio_buffer.clear()
    
    def _processing_worker(self):
        """Background worker thread for processing audio."""
        logger.info("Processing worker thread started")
        
        while self.is_running:
            try:
                # Get processing task with timeout
                task = self.processing_queue.get(timeout=1.0)
                
                if task['type'] == 'interim':
                    self._process_interim_audio(task['audio'])
                elif task['type'] == 'final':
                    self._process_final_audio(task['audio'], task.get('session_id'))
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in processing worker: {e}")
                self.send_error(f"Processing error: {str(e)}")
        
        logger.info("Processing worker thread stopped")
    
    def _process_interim_audio(self, audio):
        """Process audio for interim results."""
        try:
            # Skip if audio is too short
            duration = len(audio) / self.sample_rate
            if duration < self.min_audio_length:
                return
            
            # Transcribe audio
            segments, info = self.model.transcribe(
                audio,
                language="en",
                task="transcribe",
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=500)
            )
            
            # Combine segments into text
            text_parts = []
            total_confidence = 0
            segment_count = 0
            
            for segment in segments:
                text_parts.append(segment.text.strip())
                if hasattr(segment, 'avg_logprob'):
                    total_confidence += segment.avg_logprob
                    segment_count += 1
            
            if text_parts:
                text = " ".join(text_parts).strip()
                confidence = np.exp(total_confidence / segment_count) if segment_count > 0 else 0.5
                
                self.send_interim_result(text, confidence)
            
        except Exception as e:
            logger.error(f"Error processing interim audio: {e}")
    
    def _process_final_audio(self, audio, session_id):
        """Process audio for final results."""
        try:
            # Skip if audio is too short
            duration = len(audio) / self.sample_rate
            if duration < self.min_audio_length:
                self.send_final_result("", 0.0, session_id)
                return
            
            # Transcribe with higher quality settings for final result
            segments, info = self.model.transcribe(
                audio,
                language="en",
                task="transcribe",
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=300),
                word_timestamps=True
            )
            
            # Combine segments into text with word-level timestamps
            text_parts = []
            words = []
            total_confidence = 0
            segment_count = 0
            
            for segment in segments:
                text_parts.append(segment.text.strip())
                if hasattr(segment, 'avg_logprob'):
                    total_confidence += segment.avg_logprob
                    segment_count += 1
                
                # Extract word-level timestamps if available
                if hasattr(segment, 'words'):
                    for word in segment.words:
                        words.append({
                            'word': word.word,
                            'start': word.start,
                            'end': word.end,
                            'probability': getattr(word, 'probability', 0.5)
                        })
            
            text = " ".join(text_parts).strip()
            confidence = np.exp(total_confidence / segment_count) if segment_count > 0 else 0.5
            
            self.send_final_result(text, confidence, session_id, words)
            
        except Exception as e:
            logger.error(f"Error processing final audio: {e}")
            self.send_error(f"Final processing error: {str(e)}")
    
    def send_interim_result(self, text, confidence):
        """Send interim transcription result."""
        message = {
            "type": "interim",
            "text": text,
            "confidence": float(confidence),
            "timestamp": time.time()
        }
        self._send_message(message)
    
    def send_final_result(self, text, confidence, session_id, words=None):
        """Send final transcription result."""
        message = {
            "type": "final",
            "text": text,
            "confidence": float(confidence),
            "session_id": session_id,
            "timestamp": time.time()
        }
        if words:
            message["words"] = words
        self._send_message(message)
    
    def send_error(self, error_message):
        """Send error message."""
        message = {
            "type": "error",
            "message": error_message,
            "timestamp": time.time()
        }
        self._send_message(message)
    
    def send_status(self, status):
        """Send status message."""
        message = {
            "type": "status",
            "status": status,
            "timestamp": time.time()
        }
        self._send_message(message)
    
    def _send_message(self, message):
        """Send JSON message to stdout."""
        try:
            json_str = json.dumps(message)
            print(json_str, flush=True)
        except Exception as e:
            logger.error(f"Error sending message: {e}")

def main():
    """Main function to run the streaming transcription service."""
    logger.info("Starting Streaming Transcription Service")

    # Create service instance
    service = StreamingTranscriptionService()

    # Start the service
    if not service.start():
        logger.error("Failed to start service")
        sys.exit(1)

    try:
        # Main message processing loop
        for line in sys.stdin:
            line = line.strip()
            if not line:
                continue

            try:
                # Parse JSON message
                message = json.loads(line)
                message_type = message.get('type')

                if message_type == 'audio_chunk':
                    # Process audio chunk
                    audio_data = message.get('data')
                    sample_rate = message.get('sample_rate', 16000)

                    if audio_data:
                        service.process_audio_chunk(audio_data, sample_rate)
                    else:
                        service.send_error("Missing audio data in chunk")

                elif message_type == 'finalize':
                    # Finalize session
                    session_id = message.get('session_id')
                    service.finalize_session(session_id)

                elif message_type == 'ping':
                    # Health check
                    service.send_status("pong")

                elif message_type == 'shutdown':
                    # Graceful shutdown
                    logger.info("Received shutdown command")
                    break

                else:
                    service.send_error(f"Unknown message type: {message_type}")

            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON received: {e}")
                service.send_error(f"Invalid JSON: {str(e)}")
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                service.send_error(f"Message processing error: {str(e)}")

    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"Unexpected error in main loop: {e}")
    finally:
        # Clean shutdown
        service.stop()
        logger.info("Streaming Transcription Service stopped")

if __name__ == "__main__":
    main()
